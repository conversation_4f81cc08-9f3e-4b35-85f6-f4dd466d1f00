#let cv-color = rgb("#284967")
#set page(margin: (x: 0.9cm, y: 1.0cm))
#set text(font: "Noto Sans CJK SC", size: 10pt)
#set par(justify: true)
#let chiline = {
  v(-8pt)
  line(stroke: cv-color, length: 100%)
  v(-2pt)
}
#show "|": text(gray, " | ")
#show heading.where(level: 1): it => text(fill: rgb("#222222"), size: 20pt, it) + v(5pt)
#show heading.where(level: 2): it => text(cv-color, it) + chiline
#let item(a, b, c) = grid(
  columns: (20%, 1fr, 30%),
  align: (left, center, right),
  text(fill: rgb("#222222"), weight: "bold", a),
  text(rgb("#222222"), weight: "bold", b),
  text(rgb("#222222"), weight: "bold", c),
)

// -------------------------------------------------------------------
// 个人简历正文
// -------------------------------------------------------------------


#grid(
  columns: (1fr, auto),
  align(center)[
    = 某某某

    #set text(rgb("#333333"))

    某某岁 | 某某成员 | 某某地区
    
    某某电话 | somebody\@example.com
  ], image(width: 70pt, "profile.jpg")
)

#set text(rgb("#444444"))

== 教育背景

#item[某某年 \~ 至今][某某重点大学][某某专业（硕士）]

*主修课程：*某某核心课程、某某理论课程、某某专业课程等。

#item[某某年 \~ 某某年][某某知名大学][某某专业（本科）]

*专业成绩：*GPA 某某 / 某某

*主修课程：*某某专业课程、某某实践课程、某某选修课程、某某技能课程、某某综合课程等。

== 科研竞赛

#item[某某年某月 \~ 某某年某月][某某全国性竞赛 / 某某级别奖项][某某职责]

*项目背景：*针对某某社会现象开展深入研究，围绕某某主题进行系统分析，为解决某某实际问题提供建设性意见。

*项目内容：*运用某某专业软件处理某某规模数据，采用某某分析方法进行数据处理，使用某某模型进行分析，最终形成完整的研究报告。通过某某技术手段提升了某某效率，获得某某评价。

#item[某某年某月 \~ 某某年某月][某某创新项目 / 某某等级][项目负责人]

*项目背景：*致力于解决某某领域存在的某某问题，旨在为某某发展方向提供某某参考建议。

*项目内容：*采用某某研究方法，针对某某地区开展某某调查。通过某某途径收集某某数量的有效数据。使用某某分析工具进行数据处理，运用某某分析方法得出某某结论，完成某某成果输出。

== 实习经历

#item[某某年某月 \~ 某某年某月][某某知名企业][某某岗位实习生]

*工作内容1：*负责某某具体工作，完成某某工作目标，获得某某认可。

*工作内容2：*参与某某项目开展，熟练使用某某系统，针对某某数据进行分析，制定某某策略。

== 实践经历

#item[某某年某月 \~ 至今][某某机构某某部门][某某职务]

*工作职责1：*负责某某具体事务，合理安排某某工作。

*工作职责2：*参与组织某某活动，开展某某工作，达成某某目标。

#item[某某年某月 \~ 某某年某月][某某志愿服务项目][志愿者]
参与某某活动的现场协调与管理工作，完成某某服务任务。

== 技能特长

- *语言能力：*某某等级证书，某某水平证明
- *专业技能：*熟练使用某某办公软件，掌握某某专业技能，能够运用某某工具进行某某操作
- *开发能力：*熟悉某某专业软件，掌握某某编程语言

== 自我评价

具有某某性格特征，拥有某某专业能力；善于某某，擅长某某；具备某某素质，注重某某发展，期望在某某领域有所建树。