#import "template.typ": *

// 主题颜色
#let theme-color = rgb("#26267d")
#let icon = icon.with(fill: theme-color)

// 设置图标, 来源: https://fontawesome.com/icons/
#let fa-award = icon("icons/fa-award.svg")
#let fa-building-columns = icon("icons/fa-building-columns.svg")
#let fa-code = icon("icons/fa-code.svg")
#let fa-envelope = icon("icons/fa-envelope.svg")
#let fa-github = icon("icons/fa-github.svg")
#let fa-graduation-cap = icon("icons/fa-graduation-cap.svg")
#let fa-linux = icon("icons/fa-linux.svg")
#let fa-phone = icon("icons/fa-phone.svg")
#let fa-windows = icon("icons/fa-windows.svg")
#let fa-wrench = icon("icons/fa-wrench.svg")
#let fa-work = icon("icons/fa-work.svg")

// 设置简历选项与头部
#show: resume.with(
  // 字体和基准大小
  size: 10pt,
  // 标题颜色
  theme-color: theme-color,
  // 控制纸张的边距
  margin: (
    top: 1.5cm,
    bottom: 2cm,
    left: 2cm,
    right: 2cm,
  ),

  // 如果需要姓名及联系信息居中，请删除下面关于头像的三行参数，并取消header-center的注释
  //header-center: true,

  // 如果不需要头像，则将下面三行的参数注释或删除
  photograph: "profile.jpg",
  photograph-width: 10em,
  gutter-width: 2em,
)[
  = 蔡鹏

  #info(
    color: theme-color,
    (
      icon: fa-phone,
      content: "(+86) 187-7159-3886",
    ),
    (
      icon: fa-building-columns,
      content: "武汉大学",
    ),
    (
      icon: fa-graduation-cap,
      content: "全球健康学",
    ),
    (
      icon: fa-envelope,
      content: "<EMAIL>",
      link: "mailto:<EMAIL>",
    ),
   
  )
][
  #h(2em)

     作为一名跨学科专业的学生，系统掌握本专业核心课程的同时，更遵循兴趣自主研习计算机科学核心领域，包括数据结构与算法、机器学习等课程（当下正在学习深度学习），跟随相关开源项目教程实现了一个GPT-2 124M模型。熟练掌握多种编程语言及其应用开发框架，建立了扎实的计算机科学理论基础与实践能力。同时积极投身"挑战杯"创新创业大赛、美国大学生数学建模竞赛等多项跨学科实践活动，培养了卓越的团队协作能力与科研创新思维，展现了较强的学科交叉融合能力和自主学习能力。
]


== #fa-graduation-cap 教育背景

#sidebar(with-line: true, side-width: 15%)[
  #stack(
    spacing: 0.5em,
    align(right)[22 年 09 月],
    align(right)[25 年 06 月]
  )
][
  *武汉大学* · 公共卫生学院 · 全球健康学 \
  GPA: 8 / 29 ，3.72
]


== #fa-wrench 专业技能

#sidebar(with-line: false, side-width: 12%)[
  *掌握*  // 精通的核心技能，展示强项
  
  *熟悉*  // 了解并能应用的技能，展示广度
  
  *擅长*  // 特别擅长的应用或分析能力，突出实际能力
][
  Python , Tensorflow，机器学习，深度学习，大模型；Java , Java Web 开发, Android 开发  // 基于你的"掌握"内容，列出技术技能
  
  数学建模，医药数据统计分析, 社会健康学理论  // 基于你的"熟悉"内容，强调理论知识
  
  医疗资源异构数据处理、数据挖掘   // 基于你的"擅长"内容，突出分析和应用能力
]



== #fa-award 获奖情况

#item(
  [ *第十六届"华中杯"大学生数学建模挑战赛* ],
  [ *三等奖* ],
  date[ 24 年 05 月 ],
)

#item(
  [ * 软著：社区用药智护系统
[简称:药安心]* ],
  [ *国家版权局* ],
  date[ 25 年 01 月 ],
  // 项目：基于"三医"协同视角的基层用药实证研究
)

#item(
  [ *武汉大学第十六届"自强杯"大学生课外学术科技作品竞赛* ],
  [ *一等奖* ],
  date[ 25 年 03 月 ],
  // 作为队长，项目：三医协同激活基层健康"微循环"评估体系构建
)





== #fa-code 项目经历
#item(
  [ *#text(weight: "bold", size: 1.2em, fill: rgb("#8B4513"))[老年人常见病药物使用风险评估与管理策略优化研究]* ],
  [ *大创项目--省级优秀* ],
  date[ 24 年 07 月 – 25 年 12 月  ],
)

#tech[ Fp-growth 关联规则挖掘 , 医疗机构异构数据分析, Python, R ]

- 整合超百万级电子处方数据，构建老年人用药模式及风险评估数据库
- 应用无监督学习，识别高风险药物联用模式
- 通过队列研究和问卷调研，提出优化药事管理流程、强化用药指导及提升患者素养的策略

#item(
  [ *“三医”协同治理下的药物政策影响评估研究* ],
  [ *国家卫健委统计信息中心* ],
  date[ 24 年 06 月 – 24 年 12 月 ],
)

#tech[ 基层医疗机构大数据, Apriori , HHI 指数, 政策评估模型, Python ]

- 构建“三医”协同治理评估框架，识别药物政策在目录、采购、供应、使用等环节的协同瓶颈
- 应用 Apriori、HHI 指数等模型，量化分析区域用药需求与政策目录的匹配度，以及医疗机构的采购行为模式
- 评估基层药品的实际可及性与可支付性，为完善国家药物政策、提升治理效能提供实证依据

#item(
  [ *基层医疗卫生机构用药现状和药物采购政策影响研究* ],
  [ *核心研究项目* ],
  date[ 24 年 01 月 – 24 年 12 月 ],
)

#tech[ 政策影响评估, 定量分析, 随机效用模型(RUM), 计量经济学, Python ]

- 评估国家药品集采政策对基层药品供应、配备、支付、使用全链条的影响
- 整合分析5省4304家基层医疗机构采购数据与百万级零售处方，量化“可替代性短缺”与“饱和效应”等非预期问题
- 运用随机效用模型(RUM)等方法，揭示基层机构在集采背景下的药品配备决策逻辑与行为结构
- 为完善国家基本药物制度、优化基层药品供应保障体系提供数据驱动的政策建议



== #fa-building-columns 校园经历

#item(
  [ *武汉大学社团指导中心公关联络组职委* ],
  [],
  date[ 24 年 07 月 – 25 年 06 月 ],
)

#item(
  [ *武汉大学2023、2024年社会活动积极分子* ],
  [],
  date[ 23 年 06 月 – 25 年 06 月 ],
)

#item(
  [ *洋光教育法语课程班优秀* ],
  [],
  date[ 25 年 03 月 – 25 年 06 月 ],
  
)


