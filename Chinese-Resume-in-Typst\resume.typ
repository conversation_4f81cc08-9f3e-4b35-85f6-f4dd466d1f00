#import "template.typ": *

// 主题颜色
#let theme-color = rgb("#26267d")
#let icon = icon.with(fill: theme-color)

// 设置图标, 来源: https://fontawesome.com/icons/
#let fa-award = icon("icons/fa-award.svg")
#let fa-building-columns = icon("icons/fa-building-columns.svg")
#let fa-code = icon("icons/fa-code.svg")
#let fa-envelope = icon("icons/fa-envelope.svg")
#let fa-github = icon("icons/fa-github.svg")
#let fa-graduation-cap = icon("icons/fa-graduation-cap.svg")
#let fa-linux = icon("icons/fa-linux.svg")
#let fa-phone = icon("icons/fa-phone.svg")
#let fa-windows = icon("icons/fa-windows.svg")
#let fa-wrench = icon("icons/fa-wrench.svg")
#let fa-work = icon("icons/fa-work.svg")

// 设置简历选项与头部
#show: resume.with(
  // 字体和基准大小
  size: 10pt,
  // 标题颜色
  theme-color: theme-color,
  // 控制纸张的边距
  margin: (
    top: 1.5cm,
    bottom: 2cm,
    left: 2cm,
    right: 2cm,
  ),

  // 如果需要姓名及联系信息居中，请删除下面关于头像的三行参数，并取消header-center的注释
  //header-center: true,

  // 如果不需要头像，则将下面三行的参数注释或删除
  photograph: "profile.jpg",
  photograph-width: 10em,
  gutter-width: 2em,
)[
  = 蔡鹏

  #info(
    color: theme-color,
    (
      icon: fa-phone,
      content: "(+86) 187-7159-3886",
    ),
    (
      icon: fa-building-columns,
      content: "武汉大学",
    ),
    (
      icon: fa-graduation-cap,
      content: "全球健康学",
    ),
    (
      icon: fa-envelope,
      content: "<EMAIL>",
      link: "mailto:<EMAIL>",
    ),
   
  )
][
  #h(2em)

     作为一名跨学科专业的学生，系统掌握本专业核心课程的同时，更遵循兴趣自主研习计算机科学核心领域，包括数据结构与算法、操作系统及计算机网络等理论基础。在此过程中，熟练掌握多种编程语言及其应用开发框架，建立了扎实的计算机科学理论基础与实践能力。同时积极投身"挑战杯"创新创业大赛、美国大学生数学建模竞赛等多项跨学科实践活动，培养了卓越的团队协作能力与科研创新思维，展现了较强的学科交叉融合能力和自主学习能力。
]


== #fa-graduation-cap 教育背景

#sidebar(with-line: true, side-width: 12%)[
  22 年 09 月

  25 年 06 月
][
  *武汉大学* · 公共卫生学院 · 全球健康学

  GPA: 8 / 29 ，3.69
]


== #fa-wrench 专业技能

#sidebar(with-line: false, side-width: 12%)[
  *掌握*  // 精通的核心技能，展示强项
  
  *熟悉*  // 了解并能应用的技能，展示广度
  
  *擅长*  // 特别擅长的应用或分析能力，突出实际能力
][
  Java , Python , Java Web 开发, Android 开发  // 基于你的"掌握"内容，列出技术技能
  
  医药相关政策, 社会健康学理论, 数学建模  // 基于你的"熟悉"内容，强调理论知识
  
  医药相关政策分析, 医疗资源相关模型构建, 机器学习的健康应用  // 基于你的"擅长"内容，突出分析和应用能力
]



== #fa-award 获奖情况

#item(
  [ *第十六届"华中杯"大学生数学建模挑战赛* ],
  [ *三等奖* ],
  date[ 24 年 05 月 ],
)

#item(
  [ *武汉大学第十六届"自强杯"大学生课外学术科技作品竞赛* ],
  [ *一等奖* ],
  date[ 25 年 03 月 ],
  // 作为队长，项目：三医协同激活基层健康"微循环"评估体系构建
)

#item(
  [ *武汉大学学生社会实践活动十佳调研报告* ],
  [ *十佳-省级提名* ],
  date[ 24 年 09 月 ],
  // 项目：基于"三医"协同视角的基层用药实证研究
)



== #fa-code 项目经历
#item(
  [ *#text(weight: "bold", size: 1.2em, fill: rgb("#8B4513"))[老年人常见病药物使用风险评估与管理策略优化研究]* ],
  [ *大创项目--省级优秀* ],
  date[ 24 年 07 月 – 26 年 03 月  ],
)

#tech[ 关联规则挖掘 , 真实世界数据分析, Python, R ]

依托大规模真实世界电子处方数据，研究社区老年人常见病用药风险，开发智能预警模型，并优化药事管理策略。

- 整合超百万级电子处方数据，构建老年人用药模式及风险评估数据库
- 应用机器学习 ，开发高风险药物联用模式的智能识别与预警系统
- 通过队列研究和问卷调研，提出优化药事管理流程、强化用药指导及提升患者素养的策略


#item(
  [ *#text(weight: "bold", size: 1.2em, fill: rgb("#8B4513"))[社区老年人药物联用风险的系统综述与实证研究]* ],
  [ *学术论文* ],
  date[ 正在投稿中 ],
)
#tech[ 系统综述, 网络 Meta 分析, 聚类分析, Python, R ]
通过系统综述与大规模处方数据实证分析，揭示中国中部地区社区老年人药物联用模式、风险特征及影响因素，为优化用药安全管理提供依据。
- 通过系统文献回顾和网络 Meta 分析，识别心血管等常见疾病的高风险药物联用组合，验证高风险联用模式

- 提出分层风险预警机制、社区处方实时监测平台及季节性风险管理等优化建议


== #fa-building-columns 校园经历

#item(
  [ *武汉大学社团指导中心公关联络组职委* ],
  [],
  date[ 24 年 07 月 – 25 年 06 月 ],
)

#item(
  [ *武汉大学2023、2024年社会活动积极分子* ],
  [],
  date[ 23 年 06 月 – 25 年 06 月 ],
)

#item(
  [ *洋光教育法语课程班优秀* ],
  [],
  date[ 25 年 03 月 – 25 年 06 月 ],
  
)


